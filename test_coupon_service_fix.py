#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试CouponService修复
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

def test_coupon_service_import():
    """测试CouponService导入"""
    try:
        from app.service.coupon import CouponService
        from app.schemas.order import OrderItemBase
        print("✓ CouponService和OrderItemBase导入成功")
        return True
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_method_signature():
    """测试方法签名"""
    try:
        from app.service.coupon import CouponService
        import inspect
        
        # 检查coupon_pricing方法
        sig = inspect.signature(CouponService.coupon_pricing)
        params = list(sig.parameters.keys())
        expected = ['session', 'user_id', 'order_items', 'coupon_usage_record_ids']
        
        if params == expected:
            print("✓ coupon_pricing方法签名正确")
            return True
        else:
            print(f"✗ coupon_pricing方法签名错误: {params}")
            return False
    except Exception as e:
        print(f"✗ 方法签名检查失败: {e}")
        return False

def test_convert_method_signature():
    """测试_convert_order_items_to_dict方法签名"""
    try:
        from app.service.coupon import CouponService
        import inspect
        
        # 检查_convert_order_items_to_dict方法
        sig = inspect.signature(CouponService._convert_order_items_to_dict)
        params = list(sig.parameters.keys())
        expected = ['session', 'order_items']
        
        if params == expected:
            print("✓ _convert_order_items_to_dict方法签名正确")
            return True
        else:
            print(f"✗ _convert_order_items_to_dict方法签名错误: {params}")
            return False
    except Exception as e:
        print(f"✗ _convert_order_items_to_dict方法签名检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试CouponService修复...")
    print("=" * 50)
    
    tests = [
        ("导入测试", test_coupon_service_import),
        ("coupon_pricing方法签名测试", test_method_signature),
        ("_convert_order_items_to_dict方法签名测试", test_convert_method_signature),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if test_func():
            passed += 1
        else:
            print(f"  {test_name} 失败")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！修复成功完成。")
        return True
    else:
        print("❌ 部分测试失败，请检查修复。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
