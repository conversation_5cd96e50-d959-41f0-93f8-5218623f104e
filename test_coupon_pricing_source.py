#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优惠券计价接口source参数
"""

import requests
import json

def test_coupon_pricing_with_source():
    """测试带source参数的优惠券计价API"""
    url = "http://127.0.0.1:8000/api/v1/wx/coupon/coupon-pricing"
    
    # 测试数据 - 不带source参数
    data_without_source = {
        "user_id": 3,
        "products": [{"product_id": 213, "quantity": 2}],
        "coupon_usage_record_ids": []
    }
    
    # 测试数据 - 带source参数
    data_with_source = {
        "user_id": 3,
        "products": [{"product_id": 213, "quantity": 2}],
        "coupon_usage_record_ids": [],
        "source": "admin_onsite"
    }
    
    # 测试数据 - source为null
    data_with_null_source = {
        "user_id": 3,
        "products": [{"product_id": 213, "quantity": 2}],
        "coupon_usage_record_ids": [],
        "source": None
    }
    
    headers = {
        "Content-Type": "application/json",
        "token": "test_token"  # 需要替换为有效的token
    }
    
    test_cases = [
        ("不带source参数", data_without_source),
        ("带source参数(admin_onsite)", data_with_source),
        ("source为null", data_with_null_source)
    ]
    
    for test_name, data in test_cases:
        print(f"\n测试: {test_name}")
        print(f"请求数据: {json.dumps(data, ensure_ascii=False)}")
        
        try:
            response = requests.post(url, json=data, headers=headers)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✓ {test_name} - API调用成功")
                # 可以进一步检查返回数据结构
                if "data" in result and "pricing_result" in result["data"]:
                    print(f"  订单总金额: {result['data']['pricing_result']['order']['total_amount']}")
                    print(f"  应付金额: {result['data']['pricing_result']['order']['payable_amount']}")
            else:
                print(f"✗ {test_name} - API调用失败: {response.status_code}")
                print(f"  错误信息: {response.text}")
                
        except Exception as e:
            print(f"✗ {test_name} - 请求异常: {e}")

def test_schema_validation():
    """测试请求模型验证"""
    try:
        from app.schemas.coupon import CouponPricingRequest
        
        # 测试不带source的请求
        request1 = CouponPricingRequest(
            user_id=3,
            products=[{"product_id": 213, "quantity": 2}],
            coupon_usage_record_ids=[]
        )
        print("✓ 不带source参数的请求模型验证通过")
        print(f"  source值: {request1.source}")
        
        # 测试带source的请求
        request2 = CouponPricingRequest(
            user_id=3,
            products=[{"product_id": 213, "quantity": 2}],
            coupon_usage_record_ids=[],
            source="admin_onsite"
        )
        print("✓ 带source参数的请求模型验证通过")
        print(f"  source值: {request2.source}")
        
        return True
        
    except Exception as e:
        print(f"✗ 请求模型验证失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试优惠券计价接口source参数...")
    print("=" * 60)
    
    # 测试请求模型
    print("\n1. 测试请求模型验证:")
    schema_ok = test_schema_validation()
    
    # 测试API调用（需要服务器运行）
    print("\n2. 测试API调用:")
    print("注意: 需要服务器运行且提供有效token")
    # test_coupon_pricing_with_source()
    
    print("\n" + "=" * 60)
    if schema_ok:
        print("🎉 请求模型测试通过！source参数添加成功。")
    else:
        print("❌ 请求模型测试失败。")

if __name__ == "__main__":
    main()
