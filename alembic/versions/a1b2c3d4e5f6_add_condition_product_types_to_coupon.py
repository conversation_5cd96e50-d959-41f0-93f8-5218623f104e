"""add condition_product_types field to coupon table

Revision ID: a1b2c3d4e5f6
Revises: f9a8b2c3d4e5
Create Date: 2025-09-13 10:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'a1b2c3d4e5f6'
down_revision: Union[str, None] = 'f9a8b2c3d4e5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('coupons', sa.Column('condition_product_types', mysql.JSON(), nullable=True, comment='条件产品类型，存储MealType值列表'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('coupons', 'condition_product_types')
    # ### end Alembic commands ###
