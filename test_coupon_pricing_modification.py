#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优惠券计价方法修改
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

def test_imports():
    """测试导入是否正常"""
    try:
        from app.service.coupon import CouponService
        from app.service.order import OrderService
        from app.schemas.order import OrderItemBase, PreOrderResult
        print("✓ 所有导入成功")
        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_method_signatures():
    """测试方法签名是否正确"""
    try:
        from app.service.coupon import CouponService
        from app.service.order import OrderService
        import inspect
        
        # 检查coupon_pricing方法签名
        coupon_pricing_sig = inspect.signature(CouponService.coupon_pricing)
        expected_params = ['session', 'user_id', 'order_items', 'coupon_usage_record_ids']
        actual_params = list(coupon_pricing_sig.parameters.keys())
        
        if actual_params == expected_params:
            print("✓ CouponService.coupon_pricing 方法签名正确")
        else:
            print(f"✗ CouponService.coupon_pricing 方法签名错误")
            print(f"  期望: {expected_params}")
            print(f"  实际: {actual_params}")
            return False
            
        # 检查pre_order方法是否存在
        if hasattr(OrderService, 'pre_order'):
            pre_order_sig = inspect.signature(OrderService.pre_order)
            pre_order_params = list(pre_order_sig.parameters.keys())
            print(f"✓ OrderService.pre_order 方法存在，参数: {pre_order_params}")
        else:
            print("✗ OrderService.pre_order 方法不存在")
            return False
            
        return True
    except Exception as e:
        print(f"✗ 方法签名检查失败: {e}")
        return False

def test_api_modification():
    """测试API修改"""
    try:
        # 检查API文件是否包含OrderService导入
        with open('app/api/v1/wechat_mini_app/coupon/coupon.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        if 'from app.service.order import OrderService' in content:
            print("✓ API文件已导入OrderService")
        else:
            print("✗ API文件未导入OrderService")
            return False
            
        if 'OrderService.pre_order' in content:
            print("✓ API文件已调用OrderService.pre_order")
        else:
            print("✗ API文件未调用OrderService.pre_order")
            return False
            
        return True
    except Exception as e:
        print(f"✗ API修改检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试优惠券计价方法修改...")
    print("=" * 50)
    
    tests = [
        ("导入测试", test_imports),
        ("方法签名测试", test_method_signatures),
        ("API修改测试", test_api_modification),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if test_func():
            passed += 1
        else:
            print(f"  {test_name} 失败")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！修改成功完成。")
        return True
    else:
        print("❌ 部分测试失败，请检查修改。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
