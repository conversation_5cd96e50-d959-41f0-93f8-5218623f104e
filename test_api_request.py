#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API请求
"""

import requests
import json

def test_coupon_pricing_api():
    """测试优惠券计价API"""
    url = "http://127.0.0.1:8000/api/v1/wx/coupon/coupon-pricing"
    
    # 测试数据
    data = {
        "user_id": 3,
        "products": [{"product_id": 213, "quantity": 2}],
        "coupon_usage_record_ids": []
    }
    
    headers = {
        "Content-Type": "application/json",
        "token": "your_test_token_here"  # 需要替换为有效的token
    }
    
    try:
        response = requests.post(url, json=data, headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            print("✓ API调用成功")
            return True
        else:
            print(f"✗ API调用失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"✗ 请求异常: {e}")
        return False

if __name__ == "__main__":
    test_coupon_pricing_api()
